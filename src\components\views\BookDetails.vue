<template>
  <div>
    <div v-if="!book">
      <h1 class="text-2xl font-bold mb-4">No Book Selected</h1>
      <p class="text-gray-500">Please select a book from the sidebar to view its details.</p>
    </div>
    <div v-else>
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold">{{ book.title }}</h1>
        <div class="flex gap-2">
          <Button
            v-if="authStore.canDeleteBooks"
            label="Delete Book"
            icon="pi pi-trash"
            severity="danger"
            @click="confirmDeleteBook"
          />
          <Button
            v-if="selectedChapterId && authStore.canDeleteChapters"
            label="Delete Chapter"
            icon="pi pi-trash"
            severity="danger"
            @click="deleteSelectedChapter"
          />
        </div>
      </div>
      <div class="grid grid-cols-1 lg:grid-cols-12 gap-6">
        <div class="lg:col-span-4">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-bold">Chapters & Items</h2>
            <Button
              v-if="authStore.canCreateChapters"
              v-tooltip="'Add Chapter'"
              size="small"
              icon="pi pi-plus"
              @click="openAddChapterDialog"
            />
          </div>
          <div v-if="treeData.length === 0" class="border border-surface-300 rounded-xl p-8 text-center text-gray-500">
            <i class="pi pi-book text-3xl mb-3 block"></i>
            <p class="text-lg font-medium mb-2">No chapters available</p>
            <p class="text-sm">Create your first chapter to get started</p>
          </div>
          <Tree
            v-else
            :value="treeData"
            selectionMode="single"
            v-model:selectionKeys="selectedKey"
            v-model:expandedKeys="expandedKeys"
            @node-select="onNodeSelect"
            @node-unselect="onNodeUnselect"
            @node-contextmenu="onNodeContextMenu"
            class="border border-surface-300 rounded-xl"
          >
          </Tree>
        </div>
        <div class="lg:col-span-8">
          <div v-if="selectedItemId && !editingItemId">
            <h2 class="text-lg font-bold mb-2">Item QR Code</h2>
            <div>
              <QRCodeDisplay
                :bookId="book!.id"
                :chapterId="selectedChapterId!"
                :itemId="selectedItemId"
                :onDelete="() => deleteSelectedItem()"
                :onEdit="() => startEditingItem()"
                :onEditBook="() => openEditBookDialog()"
              />
            </div>
          </div>
          <div v-else-if="editingItemId && selectedItem">
            <ItemEditForm
              :item="selectedItem"
              :bookId="book!.id"
              :chapterId="selectedChapterId!"
              :onSave="handleItemSaved"
              :onCancel="cancelEditingItem"
            />
          </div>
          <div v-else-if="selectedChapterId" class="max-w-4xl">
            <ItemForm
              v-if="authStore.canEdit"
              :chapterId="selectedChapterId"
              :onItemCreated="refreshTreeData"
            />
            <div v-else class="text-center p-8 text-gray-500">
              <i class="pi pi-lock text-3xl mb-3 block"></i>
              <p class="text-lg font-medium mb-2">Access Restricted</p>
              <p class="text-sm">You don't have permission to create or edit items</p>
            </div>
          </div>
          <p v-else class="text-gray-500">Select a chapter or item to add items.</p>
        </div>
      </div>
      <Dialog v-model:visible="showAddChapterDialog" header="Add Chapter" :style="{ width: '25vw' }">
        <InputText v-model="newChapterTitle" placeholder="Chapter Title" class="w-full" />
        <template #footer>
          <Button label="Cancel" class="p-button-text" @click="showAddChapterDialog = false" />
          <Button label="Save" @click="saveChapter" />
        </template>
      </Dialog>

      <!-- Delete Confirmation Dialog -->
      <Dialog v-model:visible="showDeleteDialog" header="Confirm Delete" :style="{ width: '30vw' }">
        <p class="mb-4">{{ deleteMessage }}</p>
        <template #footer>
          <Button label="Cancel" class="p-button-text" @click="showDeleteDialog = false" />
          <Button label="Delete" severity="danger" @click="performDelete" />
        </template>
      </Dialog>

      <!-- Book Edit Dialog -->
      <BookEditDialog
        v-model:visible="showEditBookDialog"
        :book="book"
        @save="handleBookEdit"
        @cancel="showEditBookDialog = false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMainStore } from '../../stores/main';
import { useAuthStore } from '../../stores/auth';
import type { Book } from '../../types/book';
import type { TreeNode } from 'primevue/treenode';
import ItemForm from '../common/ItemForm.vue';
import ItemEditForm from '../common/ItemEditForm.vue';
import QRCodeDisplay from '../common/QRCodeDisplay.vue';
import BookEditDialog from '../common/BookEditDialog.vue';
import Dialog from 'primevue/dialog';
import Button from 'primevue/button';
import Tree from 'primevue/tree';
import InputText from 'primevue/inputtext';
import { NodeService } from '../../service/NodeService';
import type { TreeSelectionKeys } from 'primevue/tree';
import type { ColorPalette } from '../../types/book';

const store = useMainStore();
const authStore = useAuthStore();
const route = useRoute();
const router = useRouter();
const book = computed<Book | undefined>(() => store.getBook(route.params.id as string));
const selectedItem = computed(() => {
  if (!selectedItemId.value || !book.value) return null;
  const chapter = book.value.chapters.find(c => c.id === selectedChapterId.value);
  return chapter?.items.find(i => i.id === selectedItemId.value) || null;
});
const selectedKey = ref<TreeSelectionKeys | undefined>(undefined);
const expandedKeys = ref<TreeSelectionKeys>({});
const selectedChapterId = ref<string | null>(null);
const selectedItemId = ref<string | null>(null);
const editingItemId = ref<string | null>(null);
const showAddChapterDialog = ref(false);
const newChapterTitle = ref('');
const treeData = ref<TreeNode[]>([]);

// Book edit functionality
const showEditBookDialog = ref(false);

// Delete functionality
const showDeleteDialog = ref(false);
const deleteMessage = ref('');
const nodeToDelete = ref<TreeNode | null>(null);

// Function to expand all tree nodes
const expandAllNodes = (nodes: TreeNode[]): TreeSelectionKeys => {
  const expanded: TreeSelectionKeys = {};

  const expandNode = (node: TreeNode) => {
    if (node.children && node.children.length > 0) {
      expanded[node.key!] = true;
      node.children.forEach(expandNode);
    }
  };

  nodes.forEach(expandNode);
  return expanded;
};

// Function to refresh tree data
const refreshTreeData = async () => {
  if (book.value) {
    const currentSelectedKey = selectedKey.value;
    const currentChapterId = selectedChapterId.value;
    const currentItemId = selectedItemId.value;

    treeData.value = await NodeService.getTreeNodes(book.value);

    // Expand all nodes to keep tree expanded
    expandedKeys.value = expandAllNodes(treeData.value);

    // Restore selection if it still exists
    if (currentItemId && book.value.chapters.some(ch => ch.items.some(item => item.id === currentItemId))) {
      selectedKey.value = currentSelectedKey;
      selectedChapterId.value = currentChapterId;
      selectedItemId.value = currentItemId;
    } else if (currentChapterId && book.value.chapters.some(ch => ch.id === currentChapterId)) {
      selectedKey.value = currentSelectedKey;
      selectedChapterId.value = currentChapterId;
      selectedItemId.value = null;
    } else if (currentSelectedKey) {
      // If neither item nor chapter exists anymore, clear selection
      clearSelection();
    }
  }
};

// Helper function to clear all selections
const clearSelection = () => {
  selectedKey.value = undefined;
  selectedChapterId.value = null;
  selectedItemId.value = null;
  editingItemId.value = null; // Also clear editing state
};

// Load tree data when book changes
watch(
  book,
  async (newBook) => {
    if (newBook) {
      treeData.value = await NodeService.getTreeNodes(newBook);
      // Expand all nodes when initially loading
      expandedKeys.value = expandAllNodes(treeData.value);
    } else {
      treeData.value = [];
      expandedKeys.value = {};
    }

    // Clear selections when book changes to prevent 404 errors
    clearSelection();
  },
  { immediate: true }
);

// Watch for selection key changes to handle deselection
watch(
  selectedKey,
  (newSelectedKey) => {
    // If selectedKey becomes empty or undefined, clear all selections
    if (!newSelectedKey || Object.keys(newSelectedKey).length === 0) {
      selectedChapterId.value = null;
      selectedItemId.value = null;
      editingItemId.value = null;
    }
  }
);

const onNodeSelect = (node: TreeNode) => {
  if (!book.value || !node.key) {
    clearSelection();
    return;
  }

  selectedKey.value = { [node.key]: { checked: true } };

  if (node.children) {
    // Chapter (has children)
    selectedChapterId.value = node.key;
    selectedItemId.value = null;
  } else {
    // Item (no children)
    const chapter = book.value.chapters.find(ch =>
      ch.items.some(item => item.id === node.key)
    );
    selectedChapterId.value = chapter?.id ?? null;
    selectedItemId.value = node.key;
  }
};

const onNodeUnselect = () => {
  clearSelection();
};

const openAddChapterDialog = () => {
  newChapterTitle.value = '';
  showAddChapterDialog.value = true;
};

const saveChapter = async () => {
  if (book.value && newChapterTitle.value) {
    await store.addChapter(book.value.id, { title: newChapterTitle.value });
    showAddChapterDialog.value = false;
    // Refresh tree data after adding chapter
    await refreshTreeData();
  }
};

// Book edit functions
const openEditBookDialog = () => {
  showEditBookDialog.value = true;
};

const handleBookEdit = async (updates: { title: string; description: string; colorPalette: ColorPalette }) => {
  if (book.value) {
    await store.editBook(book.value.id, updates);
    showEditBookDialog.value = false;
    // Refresh the book data
    await store.loadBooks();
  }
};

// Item editing functions
const startEditingItem = () => {
  editingItemId.value = selectedItemId.value;
};

const cancelEditingItem = () => {
  editingItemId.value = null;
};

const handleItemSaved = async (updatedItem: any) => {
  editingItemId.value = null;
  // Refresh tree data to show updated item
  await refreshTreeData();
  // Maintain selection after update
  if (updatedItem && selectedItemId.value === updatedItem.id) {
    selectedKey.value = { [updatedItem.id]: { checked: true } };
  }
};

// Delete functionality
const onNodeContextMenu = (event: any) => {
  // Context menu functionality can be added here if needed
  console.log('Context menu for node:', event.node);
};



const confirmDeleteBook = () => {
  if (!book.value) return;

  nodeToDelete.value = {
    key: book.value.id,
    label: book.value.title,
    data: 'book'
  } as TreeNode;

  deleteMessage.value = `Are you sure you want to delete the book "${book.value.title}" and all its chapters and items? This action cannot be undone.`;
  showDeleteDialog.value = true;
};

const deleteSelectedItem = () => {
  if (!selectedItemId.value || !book.value) return;

  // Find the item to get its title
  const chapter = book.value.chapters.find(ch =>
    ch.items.some(item => item.id === selectedItemId.value)
  );
  const item = chapter?.items.find(item => item.id === selectedItemId.value);

  if (!item) return;

  nodeToDelete.value = {
    key: selectedItemId.value,
    label: item.title,
    data: 'item'
  } as TreeNode;

  deleteMessage.value = `Are you sure you want to delete the item "${item.title}"?`;
  showDeleteDialog.value = true;
};

const deleteSelectedChapter = () => {
  if (!selectedChapterId.value || !book.value) return;

  // Find the chapter to get its title
  const chapter = book.value.chapters.find(ch => ch.id === selectedChapterId.value);

  if (!chapter) return;

  nodeToDelete.value = {
    key: selectedChapterId.value,
    label: chapter.title,
    data: 'chapter',
    children: chapter.items.length > 0 ? [] : undefined // Indicate if it has items
  } as TreeNode;

  if (chapter.items.length > 0) {
    deleteMessage.value = `Are you sure you want to delete the chapter "${chapter.title}" and all its ${chapter.items.length} items? This action cannot be undone.`;
  } else {
    deleteMessage.value = `Are you sure you want to delete the chapter "${chapter.title}"?`;
  }

  showDeleteDialog.value = true;
};

const performDelete = async () => {
  if (!nodeToDelete.value) {
    return;
  }

  const node = nodeToDelete.value;

  try {
    if (node.data === 'book') {
      // It's a book
      await store.deleteBook(node.key);
      // Navigate to home since the book is deleted
      router.push('/');
    } else if (node.data === 'item' || (!node.children && node.data !== 'book')) {
      // It's an item (either explicitly marked or inferred from lack of children)
      if (!book.value) return;
      const chapter = book.value.chapters.find(ch =>
        ch.items.some(item => item.id === node.key)
      );

      if (chapter) {
        await store.deleteItem(book.value.id, chapter.id, node.key);

        // Clear selection if deleted item was selected
        if (selectedItemId.value === node.key) {
          clearSelection();
        }
      }
    } else if (node.data === 'chapter' || node.children !== undefined) {
      // It's a chapter
      if (!book.value) return;
      await store.deleteChapter(book.value.id, node.key);

      // Clear selection if deleted chapter was selected
      if (selectedChapterId.value === node.key) {
        clearSelection();
      }
    }

    showDeleteDialog.value = false;
    nodeToDelete.value = null;

    // Refresh tree data after deletion (except for book deletion since we navigate away)
    if (node.data !== 'book') {
      await refreshTreeData();
    }
  } catch (error) {
    console.error('Error deleting:', error);
    // You could show an error message here
  }
};
</script>
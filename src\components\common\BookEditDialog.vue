<template>
  <Dialog :visible="visible" header="Edit Book" :style="{ width: '35vw' }" @hide="onCancel" @update:visible="$emit('update:visible', $event)">
    <div class="space-y-4">
      <div>
        <label class="block text-sm font-medium mb-1">Book Title</label>
        <InputText v-model="editTitle" placeholder="Enter book title" class="w-full" />
      </div>
      
      <div>
        <label class="block text-sm font-medium mb-1">Description</label>
        <InputText v-model="editDescription" placeholder="Enter description" class="w-full" />
      </div>
      
      <div>
        <label class="block text-sm font-medium mb-2">Color Palette</label>
        <div class="grid grid-cols-3 gap-3">
          <div class="text-center">
            <label class="block text-xs text-gray-600 mb-1">Primary</label>
            <ColorPicker v-model="editColorPalette.primary" format="hex" class="mx-auto" />
          </div>
          <div class="text-center">
            <label class="block text-xs text-gray-600 mb-1">Secondary</label>
            <ColorPicker v-model="editColorPalette.secondary" format="hex" class="mx-auto" />
          </div>
          <div class="text-center">
            <label class="block text-xs text-gray-600 mb-1">Accent</label>
            <ColorPicker v-model="editColorPalette.accent" format="hex" class="mx-auto" />
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <Button label="Cancel" class="p-button-text" @click="onCancel" />
      <Button label="Save Changes" @click="onSave" />
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import Dialog from 'primevue/dialog';
import InputText from 'primevue/inputtext';
import ColorPicker from 'primevue/colorpicker';
import Button from 'primevue/button';
import type { Book, ColorPalette } from '../../types/book';

interface Props {
  visible: boolean;
  book: Book | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'save', updates: { title: string; description: string; colorPalette: ColorPalette }): void;
  (e: 'cancel'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const editTitle = ref('');
const editDescription = ref('');
const editColorPalette = ref<ColorPalette>({
  primary: '#4f46e5',
  secondary: '#7c3aed',
  accent: '#06b6d4'
});

// Watch for book changes to populate form
watch(() => props.book, (newBook) => {
  if (newBook) {
    editTitle.value = newBook.title;
    editDescription.value = newBook.description;
    editColorPalette.value = newBook.colorPalette || {
      primary: '#4f46e5',
      secondary: '#7c3aed',
      accent: '#06b6d4'
    };
  }
}, { immediate: true });

const onSave = () => {
  if (editTitle.value.trim()) {
    emit('save', {
      title: editTitle.value.trim(),
      description: editDescription.value.trim(),
      colorPalette: editColorPalette.value
    });
  }
};

const onCancel = () => {
  emit('update:visible', false);
  emit('cancel');
};
</script>

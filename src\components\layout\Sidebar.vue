<template>
  <div class="flex flex-col h-full">
    <!-- Main Content Area -->
    <div class="flex-1 overflow-y-auto">
      <Button
        v-if="authStore.canCreateBooks"
        label="Create Book"
        icon="pi pi-plus"
        class="mb-4 w-full"
        @click="openAddBookDialog"
      />
      <div v-if="store.loading" class="text-surface-500 text-sm">Loading books...</div>
      <div v-else-if="store.error" class="text-red-500 text-sm">Error: {{ store.error }}</div>
      <div v-else-if="!store.books.length" class="text-surface-500 text-sm">No books available</div>
      <Menu v-else :model="menuItems" class="w-full !border-surface-300 !rounded-xl">
        <template #submenulabel="{item}">
          <span class="font-semibold text-primary">{{ item.label }}</span>
          
        </template>
        <template #item="{ item, props }">
          <a
            v-if="item.id"
            v-bind="props.action"
            :class="[
              'flex items-center p-3 cursor-pointer rounded-lg transition-colors duration-200 mb-1',
              store.selectedBookId === item.id
                ? 'bg-blue-100 text-blue-700 font-medium'
                : 'hover:bg-surface-100 text-surface-700'
            ]"
            @click="selectBook(item.id)"
          >
            <i class="pi pi-book mr-3 text-sm"></i>
            <span class="text-sm">{{ item.label }}</span>
          </a>
        </template>
      </Menu>
    </div>

    <!-- User Section at Bottom -->
    <div class="border-t border-surface-300 pt-4 mt-4">
      <div class="mb-3">
        <div class="flex items-center gap-2 mb-2">
          <i class="pi pi-user text-surface-500 text-sm"></i>
          <span class="text-sm font-medium text-surface-700">{{ authStore.user?.username }}</span>
        </div>
        <span class="inline-block text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full font-medium">
          {{ authStore.user?.role }}
        </span>
      </div>
      <Button
        label="Sign Out"
        icon="pi pi-sign-out"
        severity="danger"
        outlined
        size="small"
        class="w-full"
        @click="handleLogout"
      />
    </div>
    <Dialog v-model:visible="showAddBookDialog" header="Add Book" :style="{ width: '35vw' }">
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium mb-1">Book Title</label>
          <InputText v-model="newBookTitle" placeholder="Enter book title" class="w-full" />
        </div>

        <div>
          <label class="block text-sm font-medium mb-1">Description</label>
          <InputText v-model="newBookDescription" placeholder="Enter description" class="w-full" />
        </div>

        <div>
          <label class="block text-sm font-medium mb-2">Color Palette</label>
          <div class="grid grid-cols-3 gap-4">
            <div>
              <label class="block text-xs text-gray-600 mb-1">Primary</label>
              <div class="flex flex-col items-center gap-2">
                <ColorPicker v-model="newBookColorPalette.primary" format="hex" class="mx-auto" @change="ensureHashPrefix('primary')" />
                <InputText
                  v-model="newBookColorPalette.primary"
                  placeholder="#4f46e5"
                  class="w-20 text-xs text-center"
                  @input="validateHexInput('primary')"
                />
              </div>
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Secondary</label>
              <div class="flex flex-col items-center gap-2">
                <ColorPicker v-model="newBookColorPalette.secondary" format="hex" class="mx-auto" @change="ensureHashPrefix('secondary')" />
                <InputText
                  v-model="newBookColorPalette.secondary"
                  placeholder="#7c3aed"
                  class="w-20 text-xs text-center"
                  @input="validateHexInput('secondary')"
                />
              </div>
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Accent</label>
              <div class="flex flex-col items-center gap-2">
                <ColorPicker v-model="newBookColorPalette.accent" format="hex" class="mx-auto" @change="ensureHashPrefix('accent')" />
                <InputText
                  v-model="newBookColorPalette.accent"
                  placeholder="#06b6d4"
                  class="w-20 text-xs text-center"
                  @input="validateHexInput('accent')"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <Button label="Cancel" class="p-button-text" @click="showAddBookDialog = false" />
        <Button label="Save" @click="saveBook" />
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useMainStore } from '../../stores/main';
import { useAuthStore } from '../../stores/auth';
import type { MenuItem } from 'primevue/menuitem';
import Button from 'primevue/button';
import Menu from 'primevue/menu';
import Dialog from 'primevue/dialog';
import InputText from 'primevue/inputtext';
import ColorPicker from 'primevue/colorpicker';

const store = useMainStore();
const authStore = useAuthStore();
const router = useRouter();
const route = useRoute();
const showAddBookDialog = ref(false);
const newBookTitle = ref('');
const newBookDescription = ref('');
const newBookColorPalette = ref({
  primary: '#4f46e5',
  secondary: '#7c3aed',
  accent: '#06b6d4'
});

// Function to handle book selection
const selectBook = (bookId: string) => {
  store.selectBook(bookId);
  router.push(`/book/${bookId}`);
};

const menuItems = computed<MenuItem[]>(() => {
  if (!Array.isArray(store.books)) {
    return [];
  }
  return [
    {
      label: 'Books',
      items: store.books.map(book => ({
        label: book.title,
        id: book.id,
      })),
    },
  ];
});

// Sync selectedBookId with route
watch(
  () => route.params.id,
  (id) => {
    if (typeof id === 'string') {
      store.selectBook(id);
    } else {
      store.selectBook(null);
    }
  },
  { immediate: true }
);

// Load books
store.loadBooks();

const openAddBookDialog = () => {
  newBookTitle.value = '';
  newBookDescription.value = '';
  newBookColorPalette.value = {
    primary: '#4f46e5',
    secondary: '#7c3aed',
    accent: '#06b6d4'
  };
  showAddBookDialog.value = true;
};

const saveBook = async () => {
  if (newBookTitle.value) {
    await store.addBook({
      title: newBookTitle.value,
      description: newBookDescription.value,
      colorPalette: newBookColorPalette.value,
    });
    showAddBookDialog.value = false;
  }
};

const ensureHashPrefix = (colorType: 'primary' | 'secondary' | 'accent') => {
  const currentValue = newBookColorPalette.value[colorType];
  if (currentValue && !currentValue.startsWith('#')) {
    newBookColorPalette.value[colorType] = '#' + currentValue;
  }
};

const validateHexInput = (colorType: 'primary' | 'secondary' | 'accent') => {
  let value = newBookColorPalette.value[colorType];

  // Remove any non-hex characters except #
  value = value.replace(/[^#0-9a-fA-F]/g, '');

  // Ensure it starts with #
  if (value && !value.startsWith('#')) {
    value = '#' + value;
  }

  // Limit to 7 characters (#RRGGBB)
  if (value.length > 7) {
    value = value.substring(0, 7);
  }

  newBookColorPalette.value[colorType] = value;
};

const handleLogout = async () => {
  await authStore.logout();
  router.push('/auth/login');
};
</script>

<style scoped>
/* Custom styles if needed - most styling is handled by Tailwind */
</style>